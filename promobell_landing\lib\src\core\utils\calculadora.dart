import 'package:flutter/widgets.dart';

class Resolution {
  final double _standardWidth = 1440.0;
  final double _standardHeight = 748.0;

  double maxWidth(BuildContext context, double widgetWidth) {
    double result = widgetWidth / _standardWidth;

    return MediaQuery.of(context).size.width * result;
  }

  double maxHeight(BuildContext context, double widgetHeight) {
    double result = widgetHeight / _standardHeight;

    return MediaQuery.of(context).size.height * result;
  }

  double minWidth(BuildContext context, double minWidth) {
    double result = minWidth / _standardWidth;
    double result2 = MediaQuery.of(context).size.height * result;

    return result2 / 10;
  }

  double minHeight(BuildContext context, double minHeight) {
    double result = minHeight / _standardHeight;
    double result2 = MediaQuery.of(context).size.height * result;

    return result2 / 10;
  }
}

class ResolutionHelper {
  static const double _baseWidth = 1440.0;
  static const double _baseHeight = 748.0;

  static double width(BuildContext context, double designWidth) {
    return MediaQuery.of(context).size.width * (designWidth / _baseWidth);
  }

  static double height(BuildContext context, double designHeight) {
    return MediaQuery.of(context).size.height * (designHeight / _baseHeight);
  }
}
