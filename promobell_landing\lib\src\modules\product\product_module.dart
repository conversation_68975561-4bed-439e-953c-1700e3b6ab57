import 'package:flutter_modular/flutter_modular.dart';

import '../../core/bindings/core_module.dart';
import 'domain/repositories/i_product_datasource.dart';
import 'domain/repositories/i_product_repository.dart';
import 'domain/usecase/get_products.dart';
import 'infra/data/product_datasource_impl.dart';
import 'infra/repositories/product_repository_impl.dart';
import 'presentation/controllers/app_detection_controller.dart';

class ProductModule extends Module {
  @override
  List<Module> get imports => [CoreModule()];

  @override
  void exportedBinds(i) {
    i.addSingleton<IProductDatasource>(ProductDatasourceImpl.new);
    i.addSingleton<IProductRepository>(ProductRepositoryImpl.new);
    i.addSingleton(GetProduct.new);
    i.addSingleton(AppDetectionController.new);
  }
}
