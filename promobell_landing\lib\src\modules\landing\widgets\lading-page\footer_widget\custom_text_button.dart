import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';

class CustomTextButton extends StatelessWidget {
  final String text;
  final String? icon;
  final void Function()? onPressed;
  final bool comIcone;

  const CustomTextButton({required this.text, required this.onPressed, this.comIcone = false, this.icon, super.key});

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: ColorOutlet.systemBorderDisabled,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      child: Row(
        spacing: 8,
        children: [
          if (comIcone && icon != null) SvgPicture.asset(icon!),
          TextPattern.customText(text: text, fontSize: 16, color: ColorOutlet.contentTertiary),
        ],
      ),
    );
  }
}
