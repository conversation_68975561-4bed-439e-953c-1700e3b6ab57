import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:promobell_landing/src/core/theme/text_pattern.dart';
import 'package:promobell_landing/src/modules/landing/widgets/onboarding_download_section.dart';
import 'package:promobell_landing/src/modules/product/presentation/widgets/product_price_display.dart';
import 'package:promobell_landing/src/modules/product/presentation/widgets/view_in_store_button.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/helpers/parse_helper.dart';
import '../../../../core/seo/seo.dart';
import '../../../../core/theme/color_outlet.dart';
import '../../../../core/utils/format_currency.dart';
import '../../../../core/widgets/shared/no_mouse_drag_scroll_behavior.dart';
import '../../../landing/page/empty_state.dart';
import '../../../landing/widgets/lading-page/landing_header/landing_header.dart';
import '../controllers/app_detection_controller.dart';
import '../controllers/product_controller.dart';
import '../widgets/offer_platform_label.dart';
import '../widgets/product_image_card.dart';

class ProductPage extends StatefulWidget {
  const ProductPage({super.key});

  @override
  State<ProductPage> createState() => _ProductPageState();
}

class _ProductPageState extends State<ProductPage> implements Seo {
  final controller = Modular.get<ProductController>();
  final appDetectionController =
      Modular.get<AppDetectionController>();

  @override
  void initState() {
    super.initState();
    final id = parseIntFromQueryParam('id');
    controller.loadProductData(id);
    loadSeo();

    // Tentar detectar e abrir o app após carregar os dados do produto
    controller.addListener(_onProductLoaded);
  }

  void _onProductLoaded() {
    if (controller.product != null &&
        !appDetectionController.appDetectionAttempted) {
      appDetectionController.attemptAppDetection(controller.product!);
    }
  }

  @override
  void dispose() {
    controller.removeListener(_onProductLoaded);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        if (controller.loading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (controller.errorMessage != null ||
            controller.product == null) {
          return EmptyState();
        }

        return SelectionArea(
          child: Scaffold(
            backgroundColor: ColorOutlet.surface,
            body: ScrollConfiguration(
              behavior: NoMouseDragScrollBehavior(),
              child: Stack(
                children: [
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 96,
                      color: Color(0xFFFAFAFC).withValues(alpha: 80),
                    ),
                  ),
                  CustomScrollView(
                    slivers: [
                      const LandingHeader(center: true),
                      SliverToBoxAdapter(
                        child: Column(
                          children: [
                            Container(
                              color: ColorOutlet.paper,
                              child: FixedWidthFittedSection(
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(height: 32),
                                    OfferPlatformLabel(
                                      platform:
                                          controller
                                              .product!
                                              .plataforma,
                                    ),
                                    SizedBox(height: 16),
                                    ProductImageCard(
                                      imageUrl:
                                          controller
                                              .product!
                                              .urlImagem,
                                    ),
                                    SizedBox(height: 16),
                                    TextPattern.customText(
                                      text:
                                          controller.product!.titulo,
                                      fontSize: 16,
                                      fontWeightOption:
                                          FontWeightOption.semiBold,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: 16),
                                    ProductPriceDisplay(
                                      controller: controller,
                                    ),
                                    SizedBox(height: 16),
                                    if (controller
                                            .product!
                                            .precoAtual >
                                        0)
                                      ViewInStoreButton(
                                        onPressed: () {
                                          if (controller
                                              .product!
                                              .urlAfiliado
                                              .isNotEmpty) {
                                            launchUrl(
                                              Uri.parse(
                                                controller
                                                    .product!
                                                    .urlAfiliado,
                                              ),
                                            );
                                          }
                                        },
                                      ),
                                    SizedBox(height: 32),
                                  ],
                                ),
                              ),
                            ),
                            OnboardingDownloadSection(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  loadSeo() {
    if (controller.product == null) return;

    final meta = MetaSEO();
    meta.ogTitle(ogTitle: controller.product!.titulo);
    meta.description(
      description:
          "${controller.product!.descricao} - O app alerta, você economiza",
    );
    meta.ogDescription(
      ogDescription:
          "${controller.product!.descricao} - O app alerta, você economiza",
    );
    meta.ogImage(ogImage: controller.product!.urlImagem);
    meta.propertyContent(property: 'og:type', content: 'product');
    meta.propertyContent(
      property: 'og:price:amount',
      content: formatCurrency(controller.product!.precoAtual),
    );
    meta.propertyContent(
      property: 'og:price:currency',
      content: 'BRL',
    );
    meta.propertyContent(
      property: 'og:availability',
      content:
          controller.product!.precoAtual > 0
              ? 'in_stock'
              : 'out_of_stock',
    );
    meta.propertyContent(
      property: 'og:url',
      content:
          'https://promobell.com.br/product?id=${controller.product!.id}',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, ${controller.product!.plataforma}, Amazon, Shopee, Magalu, Shopee, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, ${controller.product!.categoria}, ${controller.product!.subcategoria}, buscar ofertas, economia inteligente, app para economizar, alertas inteligentes, cupons exclusivos, o app alerta você economiza, promobell',
    );

    // Adicionar descrição adicional para melhorar o SEO
    meta.propertyContent(
      property: 'og:site_name',
      content: 'Promobell - O app alerta, você economiza',
    );

    // Adicionar informações de preço com desconto
    if (controller.product!.precoAntigo >
        controller.product!.precoAtual) {
      final descontoPercentual = ((controller.product!.precoAntigo -
                  controller.product!.precoAtual) /
              controller.product!.precoAntigo *
              100)
          .toStringAsFixed(0);
      meta.propertyContent(
        property: 'product:price:savings',
        content: formatCurrency(
          controller.product!.precoAntigo -
              controller.product!.precoAtual,
        ),
      );
      meta.propertyContent(
        property: 'product:price:savings_percentage',
        content: descontoPercentual,
      );

      // Adicionar informação de desconto na descrição
      final descricaoComDesconto =
          "${controller.product!.descricao} - Com $descontoPercentual% de desconto! O app alerta, você economiza";
      meta.description(description: descricaoComDesconto);
      meta.ogDescription(ogDescription: descricaoComDesconto);
    }
  }
}
