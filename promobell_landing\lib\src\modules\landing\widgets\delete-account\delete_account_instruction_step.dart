import 'package:flutter/material.dart';
import 'package:promobell_landing/src/modules/landing/widgets/delete-account/text_info_column.dart';

class DeleteAccountInstructionStep extends StatelessWidget {
  final String imagePath;
  final String title;
  final String description;
  final bool isInverse;
  final bool isMobile;

  const DeleteAccountInstructionStep({
    required this.imagePath,
    required this.title,
    required this.description,
    this.isInverse = false,
    this.isMobile = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return !isMobile
        ? Row(
          spacing: 40,
          children: [
            if (!isInverse) Image.asset(imagePath, width: 400, height: 561),
            TextInfoColumn(title: title, description: description),
            if (isInverse) Image.asset(imagePath, width: 400, height: 561),
          ],
        )
        : Column(
          spacing: 16,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(imagePath, width: 400, height: 561),
            TextInfoColumn(title: title, description: description, isMobile: true),
          ],
        );
  }
}
