import '../theme/svg_icons.dart';

class PlatformIcons {
  static const Map<String, String> _icons = {
    'mercado livre': SvgIcons.iconMercadoLivre,
    'mercadolivre': SvgIcons.iconMercadoLivre,
    'magazine luiza': SvgIcons.iconMagalu,
    'magazineluiza': SvgIcons.iconMagalu,
    'magalu': SvgIcons.iconMagalu,
    'amazon': SvgIcons.iconAmazon,
    'shopee': SvgIcons.iconShopee,
  };

  static String fromName(String name) {
    final normalized = name.trim().toLowerCase().replaceAll(' ', '');
    final result = _icons[normalized] ?? '';
    return result;
  }
}
