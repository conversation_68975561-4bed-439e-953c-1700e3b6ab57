import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'app/app_module.dart';
import 'app/app_widget.dart';
import 'firebase_options.dart';

void main() async {
  //setUrlStrategy(PathUrlStrategy()); vai alterar o padrão # para o caminho
  setUrlStrategy(PathUrlStrategy());
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await Supabase.initialize(
    url: const String.fromEnvironment('API_URL'),
    anonKey: const String.fromEnvironment('API_ANON_KEY'),
  );

  if (kIsWeb) {
    MetaSEO().config();
  }
  runApp(ModularApp(module: AppModule(), child: const AppWidget()));
}
