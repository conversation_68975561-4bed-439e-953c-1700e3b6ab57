import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/helpers/get_platform_icon.dart';
import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/text_pattern.dart';

class OfferPlatformLabel extends StatelessWidget {
  final String platform;

  const OfferPlatformLabel({required this.platform, super.key});

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 326),
      child: Row(
        spacing: 8,
        children: [
          Container(
            height: 48,
            width: 48,
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(width: 0.5, color: ColorOutlet.systemBorderDisabled),
            ),
            child: SvgPicture.asset(PlatformIcons.fromName(platform)),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextPattern.customText(text: 'Oferta', color: ColorOutlet.contentGhost, fontSize: 12),
                TextPattern.customText(
                  text: platform,
                  lineHeight: 1.5,
                  fontWeightOption: FontWeightOption.semiBold,
                  fontSize: 14,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
