class ValidaoresFormularios {
  static String? validarNomeDoCupom(String? value) {
    if (value!.isEmpty) return 'Digite o cupom';
    if (value.length < 4) return 'O nome do cupom precisa ter pelo menos 4 caracteres';
    if (value.length > 20) return 'O nome do cupom precisa ter no maúximo 20 caracteres';
    return null;
  }

  static String? validarDescricao1(String? value) {
    if (value == null || value.isEmpty) {
      return 'Digite a descrição 1';
    }
    return null;
  }

  static String? validarDescricao2(String? value) {
    if (value == null || value.isEmpty) {
      return 'Digite a descrição 2';
    }
    return null;
  }

  static String? validarNotificacaoTitulo(String? value) {
    if (value == null || value.isEmpty) {
      return 'Digite o título da notificação';
    }
    return null;
  }

  static String? validarNotificacaoCorpo(String? value) {
    if (value == null || value.isEmpty) {
      return 'Digite o corpo da notificação';
    }
    return null;
  }

  static String? validarQuantidadeDeCupons(String? value) {
    if (value == null || value.isEmpty) {
      return 'Digite a quantidade de cupons';
    }
    return null;
  }
}
