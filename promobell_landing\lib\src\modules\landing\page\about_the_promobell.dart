import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';

import '../../../core/seo/seo.dart';
import '../../../core/theme/color_outlet.dart';
import '../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../core/widgets/shared/no_mouse_drag_scroll_behavior.dart';
import '../widgets/about-the-promobell/about_the_promobell_body.dart';
import '../widgets/lading-page/footer_widget/footer_policy_module.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';

class AboutThePromobell extends StatelessWidget implements Seo {
  const AboutThePromobell({super.key});

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: NoMouseDragScrollBehavior(),
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          backgroundColor: ColorOutlet.surface,
          body: CustomScrollView(
            slivers: [
              const LandingHeader(),
              const SliverToBoxAdapter(child: ResponsivePaddingWrapper(child: AboutThePromobellBody())),
              const SliverToBoxAdapter(child: FooterPolicyModule()),
            ],
          ),
        ),
      ),
    );
  }

  @override
  loadSeo() {
    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Sobre o Promobell');
    meta.description(
      description:
          'O Promobell é o app ideal para quem quer aproveitar ofertas de produtos, cupons de desconto e frete grátis das melhores lojas do Brasil, como Amazon, Mercado Livre e Magalu. Baixe agora na Google Play ou App Store e comece a economizar!',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Shopee, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, produtos favoritos, categorias personalizadas, buscar ofertas, personalização de perfil, economia inteligente, app de ofertas, alertas inteligentes, o app alerta você economiza, promobell',
    );
  }
}
