import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';
import '../../../../../core/widgets/buttons/store_buttons.dart';
import '../../../../../core/widgets/layout/responsive_scale_layout.dart';
import '../../../../../core/widgets/layout/scroll_reveal.dart';

class CategoryFollowPromo extends StatelessWidget {
  final void Function() onAppStorePressed;
  final void Function() onGooglePlayPressed;
  final bool isMobile;

  const CategoryFollowPromo({
    required this.onAppStorePressed,
    required this.onGooglePlayPressed,
    this.isMobile = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final sizeHeight = MediaQuery.of(context).size.width < 997;

    return ResponsiveScaleLayout(
      builder: (context, screenWidth, itemWidth, scaleFactor) {
        final textScaler = MediaQuery.textScalerOf(context);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ScrollReveal(
              offset: Offset(0, -0.5),
              child: TextPattern.customText(
                text: 'Siga e\neconomize',
                fontSize: isMobile ? math.max(32, textScaler.scale(48 * scaleFactor)) : 48,
                fontWeightOption: FontWeightOption.bold,
                color: ColorOutlet.contentSecondary,
                textAlign: TextAlign.start,
                maxLines: 2,
              ),
            ),
            const SizedBox(height: 16),
            ScrollReveal(
              offset: Offset(0, -0.5),
              child: TextPattern.customText(
                text: 'Ofertas que combinam com você. Siga categorias e receba alertas todo dia.',
                fontSize: isMobile ? math.max(16, textScaler.scale(20 * scaleFactor)) : 20,
                fontWeightOption: FontWeightOption.regular,
                color: ColorOutlet.contentSecondary,
                textAlign: TextAlign.start,
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 32),
            StoreButtons(onAppStore: onAppStorePressed, onGooglePlay: onGooglePlayPressed, expanded: sizeHeight),
          ],
        );
      },
    );
  }
}
