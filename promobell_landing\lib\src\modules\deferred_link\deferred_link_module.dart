import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/services/deferred_links_service.dart';
import 'presentation/pages/deferred_link_page.dart';

class DeferredLinkModule extends Module {
  @override
  void binds(i) {
    i.addSingleton(DeferredLinksService.new);
  }

  @override
  void routes(r) {
    // Rota para deferred links de produtos: /l/{linkId}
    r.child(
      '/l/:linkId',
      child: (context) => DeferredLinkPage(
        linkId: r.args.params['linkId'] ?? '',
      ),
      transition: TransitionType.noTransition,
    );
  }
}