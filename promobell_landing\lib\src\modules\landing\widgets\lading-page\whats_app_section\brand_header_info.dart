import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/svg_icons.dart';
import '../../../../../core/theme/text_pattern.dart';

class BrandHeaderInfo extends StatelessWidget {
  const BrandHeaderInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          spacing: 8,
          children: [
            TextPattern.customText(
              text: 'Promobell',
              fontSize: 16,
              fontWeightOption: FontWeightOption.semiBold,
              color: Colors.white,
            ),
            SvgPicture.asset(
              SvgIcons.markerVerifiedFilled,
              colorFilter: ColorFilter.mode(ColorOutlet.contentPrimary, BlendMode.srcIn),
            ),
          ],
        ),
        TextPattern.customText(
          text: 'Seguidores: 60k',
          fontSize: 11,
          fontWeightOption: FontWeightOption.regular,
          color: Colors.white,
        ),
      ],
    );
  }
}
