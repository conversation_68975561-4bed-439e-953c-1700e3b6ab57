import 'package:dartz/dartz.dart';
import 'package:promobell_landing/src/modules/category/domain/enums/category_error.dart';
import 'package:promobell_landing/src/modules/category/domain/dtos/category_details.dart';
import 'package:promobell_landing/src/modules/category/domain/repositories/i_get_category.dart';

import '../repositories/i_category_repository.dart';

class GetCategoryDetails implements IGetCategoryDetails {
  final ICategoryRepository repository;

  GetCategoryDetails(this.repository);

  @override
  Future<Either<CategoryError, CategoryDetails>> call(int id) {
    return repository.getCategoryDetailsComplete(id);
  }
}
