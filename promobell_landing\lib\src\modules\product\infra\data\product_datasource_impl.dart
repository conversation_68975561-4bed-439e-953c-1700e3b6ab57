import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/services/supabase/db/db.dart';

import '../models/product_model.dart';
import '../../domain/repositories/i_product_datasource.dart';

class ProductDatasourceImpl implements IProductDatasource {
  final SupabaseClient _supabase;
  final DB _db;

  ProductDatasourceImpl(this._supabase, this._db);

  @override
  Future<ProductModel?> getProductById(int idProduct) async {
    try {
      final data =
          await _supabase
              .from(_db.tabelaDeProdutos)
              .select()
              .eq('id', idProduct)
              .eq('ativo', true)
              .single();
      debugPrint('produto-encontrado');

      return ProductModel.fromMap(data);
    } catch (e, stackTrace) {
      debugPrint('Erro ao buscar produto com id $idProduct: $e\n$stackTrace');
      rethrow;
    }
  }
}
