import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/svg_icons.dart';
import '../../../../core/theme/text_pattern.dart';

class ViewInStoreButton extends StatelessWidget {
  final void Function()? onPressed;

  const ViewInStoreButton({this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: ColorOutlet.contentPrimary,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 326),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 8,
            children: [
              TextPattern.customText(
                text: 'Ver na loja',
                fontSize: 14,
                color: ColorOutlet.contentTertiary,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SvgPicture.asset(SvgIcons.arrowLink),
            ],
          ),
        ),
      ),
    );
  }
}
