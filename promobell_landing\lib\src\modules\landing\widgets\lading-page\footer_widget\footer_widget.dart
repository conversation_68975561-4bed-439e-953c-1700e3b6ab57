import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/footer_widget/buttons_in_row_or_column.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/footer_widget/custom_text_button.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/footer_widget/promobell_brand.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/footer_widget/text_footer.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/whats_app_section/whats_app_section_mobile.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/svg_icons.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../controller/landing_page_controller.dart';
import '../whats_app_section/whatsapp_section.dart';

class FooterWidget extends StatefulWidget {
  const FooterWidget({super.key});

  @override
  State<FooterWidget> createState() => _FooterWidgetState();
}

class _FooterWidgetState extends State<FooterWidget> {
  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 900;

    final controller = Modular.get<LandingPageController>();

    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        isDesktop
            ? Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: [0.0, 0.66, 0.66, 1.0],
                  colors: [
                    ColorOutlet.contentPrimary,
                    ColorOutlet.contentPrimary,
                    Colors.transparent,
                    Colors.transparent,
                  ],
                ),
              ),
              child: ResponsivePaddingWrapper(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    WhatsAppSectionWeb(onWhatsAppButtonPressed: controller.launchWhatsApp),
                    SizedBox(height: 150),
                    FittedBox(
                      fit: BoxFit.contain,
                      child: ButtonsInRowOrColumn(onInstagramPressed: controller.launchInstagram),
                    ),
                    const SizedBox(height: 32),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomTextButton(
                          text: 'Promobelloficial',
                          icon: SvgIcons.instagram,
                          onPressed: controller.launchInstagram,
                          comIcone: true,
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    TextFooter(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            )
            : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: [0.0, 0.5, 0.5, 1.0],
                  colors: [
                    ColorOutlet.contentPrimary,
                    ColorOutlet.contentPrimary,
                    Colors.transparent,
                    Colors.transparent,
                  ],
                ),
              ),
              child: Column(
                children: [
                  WhatsAppSectionMobile(onWhatsAppButtonPressed: controller.launchWhatsApp),
                  Container(
                    color: ColorOutlet.contentPrimary,
                    width: double.infinity,
                    child: ResponsivePaddingWrapper.mobile(
                      minPadding: 16,
                      maxWidth: 512.0,
                      child: Column(
                        spacing: 24,
                        children: [
                          PromobellBrand(),
                          ButtonsInRowOrColumn(isRow: false, onInstagramPressed: controller.launchInstagram),
                          TextFooter(),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ],
    );
  }
}
