import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../../../core/widgets/layout/scroll_reveal.dart';
import 'hero_image.dart';

class PromotionalBannerSection extends StatelessWidget {
  final double? height;
  final bool isMobile;

  const PromotionalBannerSection({this.isMobile = false, this.height, super.key});

  @override
  Widget build(BuildContext context) {
    return isMobile
        ? Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              stops: [0.0, 0.505, 0.505, 1.0],
              colors: [ColorOutlet.surface, ColorOutlet.surface, Colors.transparent, Colors.transparent],
            ),
          ),
          child: ResponsivePaddingWrapper.mobile(
            child: FittedBox(
              fit: BoxFit.cover,
              child: Stack(
                clipBehavior: Clip.none,
                alignment: Alignment.bottomCenter,
                children: [
                  ScrollReveal(
                    offset: Offset(0, 0.1),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 422.0),
                      child: Column(
                        children: [HeroImage(height: height, isComplete: true), const SizedBox(height: 220)],
                      ),
                    ),
                  ),
                  Positioned(
                    left: -220,
                    top: 438,
                    child: ScrollReveal(
                      offset: Offset(-0.3, 0),
                      child: Image.asset('assets/images/offer-card-esquerda.png', width: 454, height: 288),
                    ),
                  ),
                  Positioned(
                    right: -160,
                    child: ScrollReveal(
                      offset: Offset(0.3, 0),
                      child: Image.asset('assets/images/offer-card-direita.png', width: 454, height: 288),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
        : SizedBox(
          width: 888,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 888, minWidth: 888),
            child: Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ScrollReveal(offset: Offset(0, 0.1), child: HeroImage(height: height, isComplete: true)),
                    SizedBox(width: 105),
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(left: 14, top: 420),
                  child: ScrollReveal(
                    offset: Offset(-0.1, 0),
                    child: Image.asset('assets/images/offer-card-esquerda.png', width: 454, height: 288),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 460, top: 800),
                  child: ScrollReveal(
                    offset: Offset(0.1, 0),
                    child: Image.asset('assets/images/offer-card-direita.png', width: 454, height: 288),
                  ),
                ),
              ],
            ),
          ),
        );
  }
}
