String formatCurrency(double value) {
  String priceString = value.toStringAsFixed(2).replaceAll('.', ',');

  // Adiciona ponto para separar milhares
  final parts = priceString.split(',');
  final intPart = parts[0];
  final decimalPart = parts[1];

  // Formata a parte inteira com pontos a cada 3 dígitos
  String formattedIntPart = '';
  for (int i = 0; i < intPart.length; i++) {
    if (i > 0 && (intPart.length - i) % 3 == 0) {
      formattedIntPart += '.';
    }
    formattedIntPart += intPart[i];
  }

  return '$formattedIntPart,$decimalPart';
}
