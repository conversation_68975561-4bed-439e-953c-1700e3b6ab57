import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';

import '../../../core/seo/seo.dart';
import '../../../core/theme/color_outlet.dart';
import '../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../core/widgets/shared/no_mouse_drag_scroll_behavior.dart';
import '../controller/privacy_page_controller.dart';
import '../widgets/lading-page/footer_widget/footer_policy_module.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';
import '../widgets/privacy_page/privacy_page_body.dart';

class PrivacyPage extends StatelessWidget implements Seo {
  final PrivacyPageController controller;
  const PrivacyPage({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: NoMouseDragScrollBehavior(),
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          backgroundColor: ColorOutlet.surface,
          body: CustomScrollView(
            slivers: [
              const LandingHeader(),
              const SliverToBoxAdapter(child: ResponsivePaddingWrapper(child: PrivacyPageBody())),
              const SliverToBoxAdapter(child: FooterPolicyModule()),
            ],
          ),
        ),
      ),
    );
  }

  @override
  loadSeo() {
    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Política de Privacidade - Promobell');
    meta.description(
      description:
          'A Política de Privacidade do Promobell descreve como coletamos, usamos e protegemos suas informações pessoais.',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Shopee, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, política de privacidade, privacidade, dados pessoais, segurança, proteção de dados, app de ofertas, alertas inteligentes, o app alerta você economiza, promobell',
    );
  }
}
