import 'package:flutter/material.dart';

typedef PaddingBuilder = EdgeInsets Function(double paddingValue);

class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final PaddingBuilder paddingBuilder;
  final Map<double, double> breakpoints;
  final double minPadding;
  final double maxPadding;
  final bool useMaxWidthLogic;
  final double maxWidth;

  const ResponsivePadding({
    super.key,
    required this.child,
    required this.paddingBuilder,
    required this.breakpoints,
    this.minPadding = 10,
    this.maxPadding = 100,
    this.useMaxWidthLogic = false,
    this.maxWidth = 1040,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        final paddingValue = useMaxWidthLogic
            ? ((width - maxWidth).clamp(minPadding * 2, maxPadding * 2)) / 2
            : (width * _findPaddingPercent(width)).clamp(minPadding, maxPadding);

        return Padding(
          padding: paddingBuilder(paddingValue),
          child: child,
        );
      },
    );
  }

  double _findPaddingPercent(double width) {
    for (final entry in breakpoints.entries) {
      if (width < entry.key) {
        return entry.value;
      }
    }
    return breakpoints.values.last;
  }
}
