import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:web/web.dart' as web;

class LandingPageController with ChangeNotifier {
  var polegadasTela = 0.0;
  var polegadasCalculada = false;

  calcularPolegadas(BuildContext context) {
    if (polegadasCalculada) return polegadasTela;
    polegadasCalculada = true;

    double larguraTela = MediaQuery.of(context).size.width;
    double alturaTela = MediaQuery.of(context).size.height;
    double diagonal = sqrt(pow(larguraTela, 2) + pow(alturaTela, 2));

    polegadasTela = diagonal / 96.0;
    polegadasCalculada = true;

    notifyListeners();
    return polegadasTela;
  }

  // Método para tratar os links do rodapé
  void handleFooterLink(String linkType) {
    switch (linkType) {
      case 'termos':
        launchURL('https://promobell.com.br/termos-de-uso');
        break;
      case 'privacidade':
        launchURL('https://promobell.com.br/politica-de-privacidade');
        break;
      case 'contato':
        launchURL('https://promobell.com.br/contato');
        break;
    }
  }

  void launchPlayStore() async {
    final Uri url = Uri.parse(
      'https://play.google.com/store/apps/details?id=br.com.promobell',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir a Play Store');
    }
  }

  void launchAppStore() async {
    final Uri url = Uri.parse(
      'https://apps.apple.com/br/app/promobell-alerta-de-ofertas/id6747127292',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir a App Store');
    }
  }

  void launchURL(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir $urlString');
    }
  }

  void launchWhatsApp() async {
    final Uri url = Uri.parse(
      'https://whatsapp.com/channel/0029Vb640YsLY6dEdm01ho2W',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir o WhatsApp');
    }
  }

  void launchInstagram() async {
    final Uri url = Uri.parse(
      'https://www.instagram.com/promobelloficial',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir o WhatsApp');
    }
  }

  void redirectToStore() {
    launchPlayStore();
    launchAppStore();
  }

  // Método para navegar diretamente para URLs web
  void navigateToWebUrl(String path) {
    try {
      // Construir URL completa
      final baseUrl = 'https://promobell.com.br';
      final fullUrl =
          path.startsWith('/') ? '$baseUrl$path' : '$baseUrl/$path';

      // Navegar para a URL
      web.window.location.assign(fullUrl);
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao navegar para URL: $e');
      }
    }
  }

  // Método para configurar SEO dinâmico da landing page
  void setupLandingSEO({
    String? title,
    String? description,
    String? imageUrl,
    String? url,
  }) {
    try {
      if (kIsWeb) {
        final meta = MetaSEO();

        if (title != null) {
          meta.ogTitle(ogTitle: title);
        }

        if (description != null) {
          meta.description(description: description);
          meta.ogDescription(ogDescription: description);
        }

        if (imageUrl != null) {
          meta.ogImage(ogImage: imageUrl);
        }

        meta.propertyContent(property: 'og:type', content: 'website');

        final currentUrl = url ?? web.window.location.toString();
        meta.propertyContent(property: 'og:url', content: currentUrl);

        // Configurações específicas para o Promobell
        meta.propertyContent(
          property: 'og:site_name',
          content: 'Promobell',
        );
        meta.propertyContent(
          property: 'twitter:card',
          content: 'summary_large_image',
        );

        if (kDebugMode) {
          print('SEO configurado para: $title');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao configurar SEO: $e');
      }
    }
  }

  void navigateToCategory(String categoryId) {
    try {
      final categoryUrl = '/category?id=$categoryId';
      navigateToWebUrl(categoryUrl);
    } catch (e) {
      navigateToHome();
      if (kDebugMode) {
        print('Erro ao navegar para categoria: $e');
      }
    }
  }

  void navigateToProduct(String productId) {
    try {
      final productUrl = '/product?id=$productId';
      navigateToWebUrl(productUrl);
    } catch (e) {
      if (kDebugMode) {
        navigateToHome();
        print('Erro ao navegar para produto: $e');
      }
    }
  }

  // Método para navegar para a página inicial
  void navigateToHome() {
    try {
      navigateToWebUrl('/');
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao navegar para home: $e');
      }
    }
  }
}
