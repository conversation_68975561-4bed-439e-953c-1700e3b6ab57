import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:web/web.dart' as web;

class LandingPageController with ChangeNotifier {
  var polegadasTela = 0.0;
  var polegadasCalculada = false;

  static const String _playStoreUrl =
      'https://play.google.com/store/apps/details?id=br.com.promobell';
  static const String _appStoreUrl =
      'https://apps.apple.com/br/app/promobell/id6747127292';

  bool _hasRedirected = false;

  calcularPolegadas(BuildContext context) {
    if (polegadasCalculada) return polegadasTela;
    polegadasCalculada = true;

    double larguraTela = MediaQuery.of(context).size.width;
    double alturaTela = MediaQuery.of(context).size.height;
    double diagonal = sqrt(pow(larguraTela, 2) + pow(alturaTela, 2));

    polegadasTela = diagonal / 96.0;
    polegadasCalculada = true;

    notifyListeners();
    return polegadasTela;
  }

  // Método para tratar os links do rodapé
  void handleFooterLink(String linkType) {
    switch (linkType) {
      case 'termos':
        launchURL('https://promobell.com.br/termos-de-uso');
        break;
      case 'privacidade':
        launchURL('https://promobell.com.br/politica-de-privacidade');
        break;
      case 'contato':
        launchURL('https://promobell.com.br/contato');
        break;
    }
  }

  void launchPlayStore() async {
    final Uri url = Uri.parse(
      'https://play.google.com/store/apps/details?id=br.com.promobell',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir a Play Store');
    }
  }

  void launchAppStore() async {
    final Uri url = Uri.parse(
      'https://apps.apple.com/br/app/promobell-alerta-de-ofertas/id6747127292',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir a App Store');
    }
  }

  void launchURL(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir $urlString');
    }
  }

  void launchWhatsApp() async {
    final Uri url = Uri.parse(
      'https://whatsapp.com/channel/0029Vb640YsLY6dEdm01ho2W',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir o WhatsApp');
    }
  }

  void launchInstagram() async {
    final Uri url = Uri.parse(
      'https://www.instagram.com/promobelloficial',
    );
    if (!await launchUrl(url)) {
      throw Exception('Não foi possível abrir o WhatsApp');
    }
  }

  void checkForMobileRedirect() {
    if (_hasRedirected) return;

    // Verifica se é um dispositivo móvel e redireciona automaticamente
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _redirectToStoreByPlatform();
      });
    }
  }

  void _redirectToStoreByPlatform() {
    if (_hasRedirected) return;

    try {
      if (kIsWeb) {
        final userAgent = web.window.navigator.userAgent;

        // Verifica se é um dispositivo móvel
        bool isMobile =
            userAgent.contains('Android') ||
            userAgent.contains('iPhone') ||
            userAgent.contains('iPad') ||
            userAgent.contains('Mobile');

        if (isMobile) {
          _hasRedirected = true;

          // Redireciona automaticamente sem solicitar permissão
          if (userAgent.contains('Android')) {
            web.window.location.href = _playStoreUrl;
          } else if (userAgent.contains('iPhone') ||
              userAgent.contains('iPad')) {
            web.window.location.href = _appStoreUrl;
          } else {
            // Fallback para Android se for mobile mas não conseguir detectar
            web.window.location.href = _playStoreUrl;
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao redirecionar para loja: $e');
      }
    }
  }

  // Método para navegar diretamente para URLs web
  void navigateToWebUrl(String path) {
    if (!kIsWeb) return;

    try {
      // Construir URL completa
      final baseUrl = 'https://promobell.com.br';
      final fullUrl =
          path.startsWith('/') ? '$baseUrl$path' : '$baseUrl/$path';

      if (kDebugMode) {
        print('Navegando para: $fullUrl');
      }

      // Navegar para a URL
      web.window.location.assign(fullUrl);
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao navegar para URL: $e');
      }
    }
  }

  // Método para configurar SEO dinâmico da landing page
  void setupLandingSEO({
    String? title,
    String? description,
    String? imageUrl,
    String? url,
  }) {
    try {
      if (kIsWeb) {
        final meta = MetaSEO();

        if (title != null) {
          meta.ogTitle(ogTitle: title);
        }

        if (description != null) {
          meta.description(description: description);
          meta.ogDescription(ogDescription: description);
        }

        if (imageUrl != null) {
          meta.ogImage(ogImage: imageUrl);
        }

        meta.propertyContent(property: 'og:type', content: 'website');

        final currentUrl = url ?? web.window.location.toString();
        meta.propertyContent(property: 'og:url', content: currentUrl);

        // Configurações específicas para o Promobell
        meta.propertyContent(
          property: 'og:site_name',
          content: 'Promobell',
        );
        meta.propertyContent(
          property: 'twitter:card',
          content: 'summary_large_image',
        );

        if (kDebugMode) {
          print('SEO configurado para: $title');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao configurar SEO: $e');
      }
    }
  }

  // Método para navegar para categorias específicas
  void navigateToCategory(String categoryId) {
    try {
      final categoryUrl = '/category?id=$categoryId';
      navigateToWebUrl(categoryUrl);
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao navegar para categoria: $e');
      }
    }
  }

  // Método para navegar para produtos específicos
  void navigateToProduct(String productId) {
    try {
      final productUrl = '/product?id=$productId';
      navigateToWebUrl(productUrl);
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao navegar para produto: $e');
      }
    }
  }

  // Método para navegar para a página inicial
  void navigateToHome() {
    try {
      navigateToWebUrl('/');
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao navegar para home: $e');
      }
    }
  }

  // Método para resetar flags de detecção (útil para testes)
  void resetDetectionFlags() {
    _hasRedirected = false;
    notifyListeners();
  }
}
