import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../core/theme/svg_icons.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../controller/landing_page_controller.dart';
import 'pinned_header_delegate.dart';
import 'text_lading_page.dart';

class LandingHeader extends StatelessWidget {
  final bool center;

  const LandingHeader({super.key, this.center = false});

  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<LandingPageController>();
    return SliverPersistentHeader(
      pinned: true,
      delegate: PinnedHeaderDelegate(
        minExtent: 96,
        maxExtent: 96,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
            child: Container(
              height: 96,
              decoration: BoxDecoration(color: const Color.fromRGBO(250, 250, 252, 0.8)),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth < 950 || center) {
                    return ResponsivePaddingWrapper(
                      child: InkWell(
                        onTap: () => Modular.to.pushNamed(Modular.initialRoute),
                        child: SvgPicture.asset('assets/images/promobell.svg'),
                      ),
                    );
                  } else {
                    return ResponsivePaddingWrapper(
                      child: Row(
                        children: [
                          InkWell(
                            onTap: () => Modular.to.pushNamed(Modular.initialRoute),
                            child: SvgPicture.asset('assets/images/promobell.svg'),
                          ),
                          Spacer(),
                          TextLadingPage(
                            text: 'Início',
                            route: '/',
                            onPressed: () => Modular.to.pushNamed(Modular.initialRoute),
                          ),
                          SizedBox(width: 16),
                          TextLadingPage(
                            text: 'Sobre o Promobell',
                            route: '/sobre',
                            onPressed: () => Modular.to.pushNamed('/sobre'),
                          ),
                          SizedBox(width: 16),
                          TextLadingPage(
                            text: 'Promobelloficial',
                            onPressed: controller.launchInstagram,
                            svg: SvgIcons.instagram,
                            icon: true,
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
