import 'package:flutter/material.dart';

import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/text_pattern.dart';
import '../privacy_page/custom_rich_text.dart';

class CustomTextColumn extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool isPrimaryColor;

  const CustomTextColumn({required this.title, required this.subtitle, this.isPrimaryColor = false, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 20,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextPattern.customText(
          isSelectable: true,
          text: title,
          fontSize: 32,
          color: ColorOutlet.contentPrimary,
          fontWeightOption: FontWeightOption.bold,
        ),
        CustomRichText(text: subtitle, isPrimaryColor: isPrimaryColor),
      ],
    );
  }
}
