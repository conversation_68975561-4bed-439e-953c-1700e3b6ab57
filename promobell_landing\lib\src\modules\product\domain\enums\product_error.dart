enum ProductError { idNotProvided, invalidId, serverFailure, invalidProduct }

String translateProductError(ProductError error) {
  switch (error) {
    case ProductError.idNotProvided:
      return 'ID do produto não foi informado.';
    case ProductError.invalidId:
      return 'O ID do produto é inválido.';
    case ProductError.invalidProduct:
      return 'Produto inválido.';
    case ProductError.serverFailure:
      return 'Erro ao buscar produto.';
  }
}
