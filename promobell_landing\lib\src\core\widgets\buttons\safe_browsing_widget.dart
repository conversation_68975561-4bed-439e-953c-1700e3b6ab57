import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/text_pattern.dart';

import '../../../modules/landing/controller/landing_page_controller.dart';

class SafeBrowsingWidget extends StatelessWidget {
  final Color colorText;

  const SafeBrowsingWidget({this.colorText = ColorOutlet.contentTertiary, super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<LandingPageController>();

    return InkWell(
      onTap: () {
        controller.launchURL(
          'https://transparencyreport.google.com/safe-browsing/search?url=https:%2F%2Fpromobell.com.br%2F&hl=pt_BR',
        );
      },
      child: Row(
        spacing: 8,
        children: [
          SizedBox(child: SvgPicture.asset('assets/images/conexao-segura-svg.svg')),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextPattern.customText(lineHeight: 1.2, text: 'Navegação segura', fontSize: 12, color: colorText),
              TextPattern.customText(
                lineHeight: 1.0,
                text: 'Safe Browsing',
                fontSize: 16,
                fontWeightOption: FontWeightOption.bold,
                color: colorText,
              ),
              TextPattern.customText(lineHeight: 1.2, text: 'By Google', fontSize: 12, color: colorText),
            ],
          ),
        ],
      ),
    );
  }
}
