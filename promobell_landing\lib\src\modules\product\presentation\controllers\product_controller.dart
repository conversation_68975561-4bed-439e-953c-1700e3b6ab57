import 'package:flutter/widgets.dart';
import 'package:promobell_landing/src/modules/product/domain/usecase/get_products.dart';

import '../../domain/entities/product.dart';
import '../../domain/enums/product_error.dart';

class ProductController with ChangeNotifier {
  final GetProduct _getProduct;

  ProductController({required GetProduct getProduct}) : _getProduct = getProduct;

  Product? _product;
  Product? get product => _product;

  bool _isLoading = true;
  bool get loading => _isLoading;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  Future<void> loadProductData(int? id) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    if (id == null) {
      _errorMessage = translateProductError(ProductError.invalidId);
      _isLoading = false;
      notifyListeners();
      return;
    }

    final result = await _getProduct(id);
    result.fold(
      (error) => _errorMessage = translateProductError(error),
      (product) => _product = product,
    );

    _isLoading = false;
    notifyListeners();
  }
}
