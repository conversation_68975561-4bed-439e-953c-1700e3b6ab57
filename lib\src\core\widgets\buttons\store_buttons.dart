import 'package:flutter/material.dart';
import 'package:promobell_landing/src/core/widgets/layout/scroll_reveal.dart';

import '../../theme/svg_icons.dart';
import 'store_badge_button.dart';

class StoreButtons extends StatelessWidget {
  final VoidCallback onAppStore;
  final VoidCallback onGooglePlay;
  final bool expanded;
  final bool spacing;

  const StoreButtons({
    super.key,
    this.expanded = false,
    required this.onAppStore,
    required this.onGooglePlay,
    this.spacing = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 970;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ScrollReveal(
            offset: const Offset(-0.3, 0),
            child: StoreBadgeButton(
              isMobile: true,
              height: 11.5,
              svgIcon: SvgIcons.playStore,
              nameStore: 'Google Play',
              onAppStorePressed: onGooglePlay,
            ),
          ),
          if (spacing) const SizedBox(height: 16),
          ScrollReveal(
            offset: const Offset(0.3, 0),
            child: StoreBadgeButton(
              isMobile: true,
              height: 11.5,
              svgIcon: SvgIcons.apple,
              nameStore: 'Apple Store',
              onAppStorePressed: onAppStore,
            ),
          ),
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            height: 48,
            child: ScrollReveal(
              offset: const Offset(0.2, 0),
              child: StoreBadgeButton(
                svgIcon: SvgIcons.playStore,
                nameStore: 'Google Play',
                onAppStorePressed: onGooglePlay,
              ),
            ),
          ),
          if (spacing) const SizedBox(width: 16),
          SizedBox(
            height: 48,
            child: ScrollReveal(
              offset: const Offset(0.1, 0),
              child: StoreBadgeButton(
                svgIcon: SvgIcons.apple,
                nameStore: 'Apple Store',
                onAppStorePressed: onAppStore,
              ),
            ),
          ),
        ],
      );
    }
  }
}
