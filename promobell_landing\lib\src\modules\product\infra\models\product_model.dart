import '../../domain/entities/product.dart';

class ProductModel {
  final Map<String, dynamic> _map;

  ProductModel(this._map);

  Product toEntity() {
    return Product(
      id: _map['id'] as int,
      plataforma: _map['plataforma']?.toString() ?? '',
      urlAfiliado: _map['url_afiliado']?.toString() ?? '',
      urlImagem: _map['url_imagem']?.toString() ?? '',
      titulo: _map['titulo']?.toString() ?? '',
      categoria: _map['categoria']?.toString() ?? '',
      subcategoria: _map['subcategoria']?.toString() ?? '',
      descricao: _map['descricao']?.toString() ?? '',
      precoAtual: _parseDouble(_map['preco_atual']),
      precoAntigo: _parseDouble(_map['preco_antigo']),
      ativo: _map['ativo'] == true,
      menorPreco: _map['menor_preco'] == true,
      invalidProduct: _map['invalidProduct'] == true,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  factory ProductModel.fromMap(Map<String, dynamic> map) {
    return ProductModel(map);
  }

  static Map<String, dynamic> fromEntity(Product product) {
    return {
      'id': product.id,
      'plataforma': product.plataforma,
      'url_afiliado': product.urlAfiliado,
      'url_imagem': product.urlImagem,
      'titulo': product.titulo,
      'categoria': product.categoria,
      'subcategoria': product.subcategoria,
      'descricao': product.descricao,
      'preco_atual': product.precoAtual,
      'preco_antigo': product.precoAntigo,
      'ativo': product.ativo,
      'menor_preco': product.menorPreco,
      'invalidProduct': product.invalidProduct,
    };
  }
}
