import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../controller/landing_page_controller.dart';
import 'buttons_in_row_or_column.dart';
import 'promobell_brand.dart';
import 'text_footer.dart';

class FooterPolicyModule extends StatelessWidget {
  const FooterPolicyModule({super.key});

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 970;
    final controller = Modular.get<LandingPageController>();
    return Container(
      alignment: Alignment.bottomCenter,
      color: ColorOutlet.contentPrimary,
      child:
          isDesktop
              ? ResponsivePaddingWrapper(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 56),
                    FittedBox(
                      fit: BoxFit.contain,
                      child: ButtonsInRowOrColumn(onInstagramPressed: controller.launchInstagram),
                    ),
                    const SizedBox(height: 32),
                    TextFooter(),
                    const SizedBox(height: 32),
                  ],
                ),
              )
              : ResponsivePaddingWrapper.mobile(
                minPadding: 16,
                child: Column(
                  spacing: 24,
                  children: [
                    Padding(padding: const EdgeInsets.only(top: 56), child: PromobellBrand()),
                    ButtonsInRowOrColumn(
                      isRow: false,
                      onInstagramPressed: controller.launchInstagram,
                    ),
                    TextFooter(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
    );
  }
}
