import 'package:flutter/material.dart';
import 'package:promobell_landing/src/modules/category/domain/enitities/category_menu.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/services/supabase/db/db.dart';
import '../../domain/repositories/i_category_datasource.dart';

class CategoryDatasourceImpl implements ICategoryDatasource {
  final SupabaseClient _supabase;
  final DB _db;

  CategoryDatasourceImpl(this._supabase, this._db);

  @override
  Future<CategoryMenu?> getCategory(int idCategoria) async {
    try {
      final data =
          await _supabase
              .from(_db.tabelaDeCategorias)
              .select('id, nome')
              .eq('id', idCategoria)
              .single();
      debugPrint('categoria encontrada');

      final id = data['id'] as int;
      final nome = data['nome'] as String;

      final categoriaLocal = CategoryMenu.getCategoriaById(id);

      if (categoriaLocal == null) {
        return CategoryMenu(id: id, nome: nome, fotoPequena: '', bio: '', cor: Colors.grey);
      }

      return categoriaLocal;
    } catch (e, stackTrace) {
      debugPrint('Erro ao buscar categoria: $e - $stackTrace');

      return CategoryMenu.getCategoriaById(idCategoria);
    }
  }

  @override
  Future<int> getCategoryFollowersCount(int categoryId) async {
    final response = await _supabase.rpc(
      'count_followers',
      params: {'category_id_param': categoryId},
    );
    return response; // Retorna a contagem de seguidores
  }

  @override
  Future<int> getTotalProductsByCategory(String categoryName) async {
    try {
      final result = await _supabase.rpc(
        'get_total_produtos_por_categoria',
        params: {'categoria_nome': categoryName},
      );
      return result ?? 0;
    } catch (e, stackTrace) {
      debugPrint('Erro ao obter total de produtos da categoria "$categoryName" $e, $stackTrace,');
      return 0;
    }
  }
}

Color parseColorFromHex(String? hexColor) {
  if (hexColor == null || hexColor.isEmpty) {
    return Colors.black; // cor padrão, ou outra que preferir
  }
  var hex = hexColor.toUpperCase().replaceAll('#', '');
  if (hex.length == 6) {
    hex = 'FF$hex'; // adiciona alfa
  }
  return Color(int.parse(hex, radix: 16));
}
