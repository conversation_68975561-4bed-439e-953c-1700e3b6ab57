import 'package:dartz/dartz.dart';
import 'package:promobell_landing/src/modules/category/domain/enitities/category_menu.dart';

import '../enums/category_error.dart';
import '../dtos/category_details.dart';

abstract class ICategoryRepository {
  Future<Either<CategoryError, CategoryMenu>> getCategoryDetails(int id);
  Future<Either<CategoryError, CategoryDetails>> getCategoryDetailsComplete(int id);
}
