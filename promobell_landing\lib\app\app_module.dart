import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/modules/category/presentation/controllers/category_controller.dart';
import 'package:promobell_landing/src/modules/product/domain/usecase/get_products.dart';
import 'package:promobell_landing/src/modules/product/product_module.dart';

import '../src/modules/category/category_module.dart';
import '../src/modules/category/domain/repositories/i_get_category.dart';
import '../src/modules/landing/controller/landing_page_controller.dart';
import '../src/modules/landing/controller/privacy_page_controller.dart';
import '../src/modules/landing/landing_page_module.dart';
import '../src/modules/product/presentation/controllers/product_controller.dart';

class AppModule extends Module {
  @override
  List<Module> get imports => [ProductModule(), CategoryModule()];

  @override
  void binds(i) {
    i.addSingleton(LandingPageController.new);
    i.addSingleton(PrivacyPageController.new);
    i.addLazySingleton(
      () => ProductController(getProduct: i.get<GetProduct>()),
    );
    i.addLazySingleton(
      () => CategoryController(
        getCategoryDetails: i.get<IGetCategoryDetails>(),
      ),
    );
  }

  @override
  void routes(r) {
    r.module(Modular.initialRoute, module: LandingPageModule());
  }
}
