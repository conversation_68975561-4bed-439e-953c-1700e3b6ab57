import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:promobell_landing/src/modules/landing/widgets/onboarding_download_section.dart';

import '../../../core/theme/color_outlet.dart';
import '../../../core/theme/text_pattern.dart';
import '../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../core/widgets/shared/no_mouse_drag_scroll_behavior.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';

class EmptyState extends StatelessWidget {
  const EmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    return SelectionArea(
      child: Scaffold(
        backgroundColor: ColorOutlet.surface,
        body: ScrollConfiguration(
          behavior: NoMouseDragScrollBehavior(),
          child: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(height: 96, color: Color(0xFFFAFAFC).withValues(alpha: 80)),
              ),
              CustomScrollView(
                slivers: [
                  const LandingHeader(center: true),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        Container(
                          color: ColorOutlet.paper,
                          child: FixedWidthFittedSection(
                            child: SizedBox(
                              width: 326,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(height: 40),
                                  Lottie.asset(
                                    'assets/lottie/pesquisar-fina.json',
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                  SizedBox(height: 40),
                                  TextPattern.customText(
                                    text: 'Página não encontrada',
                                    fontSize: 16,
                                    fontWeightOption: FontWeightOption.semiBold,
                                  ),
                                  SizedBox(height: 24),
                                  TextPattern.customText(
                                    text: 'Talvez ela não existe, foi removida ou está temporariamente indisponível.',
                                    fontSize: 14,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 40),
                                ],
                              ),
                            ),
                          ),
                        ),

                        OnboardingDownloadSection(),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FixedWidthFittedSection extends StatelessWidget {
  final Widget child;
  final double width;

  const FixedWidthFittedSection({required this.child, this.width = 326, super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsivePaddingWrapper.mobile(
      minPadding: 32,
      maxWidth: width,
      child: FittedBox(fit: BoxFit.contain, child: SizedBox(width: width, child: child)),
    );
  }
}
