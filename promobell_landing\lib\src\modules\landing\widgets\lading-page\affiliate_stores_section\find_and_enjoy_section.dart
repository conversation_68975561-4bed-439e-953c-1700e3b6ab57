import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';
import '../../../../../core/widgets/layout/responsive_scale_layout.dart';
import '../../../../../core/widgets/layout/scroll_reveal.dart';

class FindAndEnjoySection extends StatelessWidget {
  final bool isMobile;

  const FindAndEnjoySection({this.isMobile = false, super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveScaleLayout(
      builder: (context, screenWidth, itemWidth, scaleFactor) {
        final textScaler = MediaQuery.textScalerOf(context);

        return ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 420),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScrollReveal(
                offset: Offset(0, 1),
                child: TextPattern.customText(
                  text: 'Encontre e\naproveite',
                  fontSize: isMobile ? math.max(32, textScaler.scale(48 * scaleFactor)) : 48,
                  fontWeightOption: FontWeightOption.bold,
                  color: ColorOutlet.contentSecondary,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: 16),
              ScrollReveal(
                offset: Offset(0, 2),
                child: TextPattern.customText(
                  text:
                      'As melhores lojas do Brasil em um só lugar. Explore ofertas, cupons e frete grátis.',
                  fontSize: isMobile ? math.max(16, textScaler.scale(20 * scaleFactor)) : 20,
                  fontWeightOption: FontWeightOption.regular,
                  color: ColorOutlet.contentSecondary,
                  maxLines: 3,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
