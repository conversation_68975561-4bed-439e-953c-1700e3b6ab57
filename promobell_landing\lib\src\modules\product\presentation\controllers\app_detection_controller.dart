import 'package:flutter/foundation.dart';
import 'package:web/web.dart' as web;

import '../../domain/entities/product.dart';

class AppDetectionController {
  bool _appDetectionAttempted = false;

  static const String _playStoreUrl =
      'https://play.google.com/store/apps/details?id=br.com.promobell';
  static const String _appStoreUrl =
      'https://apps.apple.com/br/app/promobell/id1234567890';

  bool get appDetectionAttempted => _appDetectionAttempted;

  Future<void> attemptAppDetection(Product product) async {
    if (_appDetectionAttempted) return;
    _appDetectionAttempted = true;

    // Redireciona diretamente para a loja sem tentar detectar o app
    redirectToStoreByPlatform();
  }

  void redirectToPlayStore() {
    try {
      if (kIsWeb) {
        web.window.location.replace(_playStoreUrl);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao redirecionar para loja: $e');
      }
    }
  }

  Future<void> attemptAppDetectionWithDeepLink(
    String deepLinkUrl,
  ) async {
    if (_appDetectionAttempted) return;
    _appDetectionAttempted = true;

    // Redireciona diretamente para a loja sem tentar detectar o app
    redirectToStoreByPlatform();
  }

  void redirectToStoreByPlatform() {
    try {
      if (kIsWeb) {
        final userAgent = web.window.navigator.userAgent;

        if (userAgent.contains('Android')) {
          web.window.location.replace(_playStoreUrl);
        } else if (userAgent.contains('iPhone') ||
            userAgent.contains('iPad')) {
          web.window.location.replace(_appStoreUrl);
        } else {
          web.window.location.replace(_playStoreUrl);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao redirecionar para loja: $e');
      }
    }
  }

  void reset() {
    _appDetectionAttempted = false;
  }
}
