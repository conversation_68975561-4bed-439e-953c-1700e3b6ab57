import 'package:flutter/material.dart';

import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/text_pattern.dart';

class TextInfoColumn extends StatelessWidget {
  const TextInfoColumn({super.key, required this.title, required this.description, this.isMobile = false});

  final bool isMobile;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: isMobile ? null : 561,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: isMobile ? 16 : 83),
          TextPattern.customText(
            text: title,
            color: ColorOutlet.contentPrimary,
            fontWeightOption: FontWeightOption.bold,
            fontSize: 48,
          ),
          SizedBox(height: 32),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 360),
            child: TextPattern.customText(
              text: description,
              color: ColorOutlet.contentSecondary,
              fontWeightOption: FontWeightOption.regular,
              fontSize: 16,
              lineHeight: 1.8,
            ),
          ),
          SizedBox(height: isMobile ? 56 : 0),
        ],
      ),
    );
  }
}
