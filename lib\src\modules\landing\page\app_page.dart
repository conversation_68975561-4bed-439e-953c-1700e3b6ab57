import 'package:flutter/material.dart';
import 'package:meta_seo/meta_seo.dart';

import '../../../core/seo/seo.dart';
import '../../../core/theme/color_outlet.dart';
import '../../../core/widgets/shared/no_mouse_drag_scroll_behavior.dart';
import '../controller/landing_page_controller.dart';
import '../widgets/lading-page/affiliate_stores_section/affiliate_stores_section.dart';
import '../widgets/lading-page/footer_widget/footer_widget.dart';
import '../widgets/lading-page/hero_widget/hero_widget.dart';
import '../widgets/lading-page/landing_header/landing_header.dart';

class AppPage extends StatefulWidget implements Seo {
  final LandingPageController controller;
  const AppPage({super.key, required this.controller});

  @override
  State<AppPage> createState() => _AppPageState();

  @override
  loadSeo() {
    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Promobell - O app alerta, você economiza');
    meta.description(
      description:
          'O Promobell é o app ideal para quem quer aproveitar ofertas de produtos, cupons de desconto e frete grátis das melhores lojas do Brasil, como Amazon, Mercado Livre e Magalu. Baixe agora na Google Play ou App Store e comece a economizar!',
    );
    meta.keywords(
      keywords:
          'ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Shopee, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, produtos favoritos, categorias personalizadas, buscar ofertas, economia inteligente, app de ofertas, alertas inteligentes, cupons exclusivos, o app alerta você economiza, promobell',
    );
  }
}

class _AppPageState extends State<AppPage> {
  @override
  void initState() {
    super.initState();
    widget.controller.redirectToStore();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        return SelectionArea(
          child: Scaffold(
            backgroundColor: ColorOutlet.paper,
            body: ScrollConfiguration(
              behavior: NoMouseDragScrollBehavior(),
              child: CustomScrollView(
                slivers: [
                  const LandingHeader(),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        HeroWidget(
                          onAppStorePressed:
                              widget.controller.launchAppStore,
                          onGooglePlayPressed:
                              widget.controller.launchPlayStore,
                        ),
                        const AffiliateStoresSection(),
                        FooterWidget(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
