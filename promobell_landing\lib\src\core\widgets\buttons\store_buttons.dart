import 'package:flutter/material.dart';
import 'package:promobell_landing/src/core/widgets/layout/scroll_reveal.dart';

import '../../theme/svg_icons.dart';
import 'store_badge_button.dart';

class StoreButtons extends StatelessWidget {
  final VoidCallback onAppStore;
  final VoidCallback onGooglePlay;
  final bool expanded;
  final bool spacing;

  const StoreButtons({
    super.key,
    this.expanded = false,
    required this.onAppStore,
    required this.onGooglePlay,
    this.spacing = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 900;

    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;

        final widthButton = (maxWidth - 16) / 2;
        return Row(
          mainAxisAlignment: isMobile ? MainAxisAlignment.center : MainAxisAlignment.start,
          children: [
            if (isMobile || expanded) ...[
              ScrollReveal(
                offset: Offset(-0.3, 0),
                child: SizedBox(
                  width: widthButton,
                  child: StoreBadgeButton(
                    isMobile: true,
                    svgIcon: SvgIcons.playStore,
                    nameStore: 'Google Play',
                    onAppStorePressed: onGooglePlay,
                  ),
                ),
              ),
              if (spacing) const SizedBox(width: 16),
              ScrollReveal(
                offset: Offset(0.3, 0),
                child: SizedBox(
                  width: widthButton,
                  child: StoreBadgeButton(
                    isMobile: true,
                    svgIcon: SvgIcons.apple,

                    nameStore: 'Apple Store',
                    onAppStorePressed: onAppStore,
                  ),
                ),
              ),
            ] else ...[
              ScrollReveal(
                offset: Offset(0.2, 0),
                child: StoreBadgeButton(
                  svgIcon: SvgIcons.playStore,

                  nameStore: 'Google Play',
                  onAppStorePressed: onGooglePlay,
                ),
              ),
              if (spacing) const SizedBox(width: 16),
              ScrollReveal(
                offset: Offset(0.1, 0),
                child: StoreBadgeButton(
                  svgIcon: SvgIcons.apple,

                  nameStore: 'Apple Store',
                  onAppStorePressed: onAppStore,
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
