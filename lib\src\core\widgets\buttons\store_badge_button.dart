import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../theme/color_outlet.dart';
import '../../theme/text_pattern.dart';

class StoreBadgeButton extends StatelessWidget {
  final Function() onAppStorePressed;
  final double height;
  final String nameStore;
  final String svgIcon;
  final bool isMobile;

  const StoreBadgeButton({
    super.key,
    this.height = 0,
    required this.nameStore,
    required this.svgIcon,
    required this.onAppStorePressed,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onAppStorePressed,
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: isMobile ? height : 0),
        backgroundColor: ColorOutlet.contentSecondary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
      child: FittedBox(
        fit: BoxFit.contain,
        child: StoreShortcutButton(svgIcon: svgIcon, nameStore: nameStore),
      ),
    );
  }
}

class StoreShortcutButton extends StatelessWidget {
  final String svgIcon;

  final String nameStore;

  const StoreShortcutButton({super.key, required this.svgIcon, required this.nameStore});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.asset(svgIcon),
        const SizedBox(width: 8),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            TextPattern.customText(
              text: 'Baixar no',
              color: ColorOutlet.contentTertiary,
              fontSize: 10,
              overflow: TextOverflow.ellipsis,
            ),
            TextPattern.customText(
              text: nameStore,
              lineHeight: 1.2,
              color: ColorOutlet.contentTertiary,
              fontSize: 16,
              fontWeightOption: FontWeightOption.semiBold,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ],
    );
  }
}
