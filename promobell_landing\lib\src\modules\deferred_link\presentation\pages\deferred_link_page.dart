import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:web/web.dart' as web;
import 'dart:js_interop';

import '../../../../services/deferred_links_service.dart';

class DeferredLinkPage extends StatefulWidget {
  final String linkId;

  const DeferredLinkPage({
    super.key,
    required this.linkId,
  });

  @override
  State<DeferredLinkPage> createState() => _DeferredLinkPageState();
}

class _DeferredLinkPageState extends State<DeferredLinkPage> {
  final DeferredLinksService _deferredLinksService = Modular.get<DeferredLinksService>();
  bool _loading = true;
  bool _appDetectionAttempted = false;

  @override
  void initState() {
    super.initState();
    _handleDeferredLink();
  }

  Future<void> _handleDeferredLink() async {
    try {
      // Resolver o deferred link
      final deferredData = await _deferredLinksService.resolveDeferredLink(widget.linkId);
      
      if (deferredData == null) {
        _redirectToPlayStore();
        return;
      }

      setState(() {
        _loading = false;
      });

      // Configurar SEO
      _setupSEO(deferredData);

      // Tentar detectar e abrir o app
      _attemptAppDetection(deferredData);
    } catch (e) {
      // Erro ao processar deferred link: $e
      _redirectToPlayStore();
    }
  }

  void _setupSEO(DeferredLinkData data) {
    final meta = MetaSEO();
    meta.ogTitle(ogTitle: data.title);
    meta.description(description: data.description);
    meta.ogDescription(ogDescription: data.description);
    meta.ogImage(ogImage: data.imageUrl);
    meta.propertyContent(property: 'og:type', content: 'product');
    meta.propertyContent(property: 'og:url', content: web.window.location.toString());
    
    if (data.price != null && data.price! > 0) {
      meta.propertyContent(
        property: 'og:price:amount',
        content: data.price!.toStringAsFixed(2),
      );
      meta.propertyContent(property: 'og:price:currency', content: 'BRL');
    }
  }

  void _attemptAppDetection(DeferredLinkData data) {
    if (_appDetectionAttempted) return;
    _appDetectionAttempted = true;

    // Criar deep link
    final deepLinkUrl = 'promobell://app${data.originalUrl}';
    
    // Tentar abrir o app usando um iframe invisível
    final iframe = web.HTMLIFrameElement()
      ..src = deepLinkUrl
      ..style.display = 'none'
      ..style.width = '1px'
      ..style.height = '1px';
    
    web.document.body?.append(iframe);
    
    // Aguardar um tempo para detectar se o app abre
    Future.delayed(Duration(milliseconds: 2500), () {
      // Se chegou até aqui, provavelmente o app não está instalado
      // Remover o iframe
      iframe.remove();
      
      // Mostrar a página de redirecionamento
      if (mounted) {
        _showRedirectPage(data);
      }
    });

    // Detectar se o usuário saiu da página (app abriu)
    void visibilityChangeHandler(web.Event event) {
      if (web.document.hidden == true) {
        // Usuário saiu da página, provavelmente o app abriu
        iframe.remove();
      }
    }
    web.document.addEventListener('visibilitychange', visibilityChangeHandler.toJS);
  }

  void _showRedirectPage(DeferredLinkData data) {
    final web.Element? body = web.document.body;
    if (body == null) return;

    // Limpar o conteúdo atual
    body.innerHTML = ''.toJS;

    // Criar o HTML da página de redirecionamento
    final redirectHtml = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.title}</title>
    <meta name="description" content="${data.description}">
    <meta property="og:title" content="${data.title}">
    <meta property="og:description" content="${data.description}">
    <meta property="og:image" content="${data.imageUrl}">
    <meta property="og:type" content="product">
    <meta property="og:url" content="${web.window.location.toString()}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 420px;
            width: 100%;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
        }
        
        .product-image {
            width: 140px;
            height: 140px;
            object-fit: cover;
            border-radius: 16px;
            margin: 0 auto 24px;
            display: block;
            border: 3px solid #f8f9fa;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }
        
        .title {
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .description {
            font-size: 15px;
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .price {
            font-size: 24px;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 32px;
            padding: 12px 20px;
            background: #ffeaa7;
            border-radius: 12px;
            display: inline-block;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 36px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }
        
        .download-btn:active {
            transform: translateY(-1px);
        }
        
        .app-info {
            font-size: 13px;
            color: #95a5a6;
            line-height: 1.5;
        }
        
        .features {
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #ecf0f1;
        }
        
        .feature {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .feature-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                padding: 30px 24px;
            }
            
            .title {
                font-size: 20px;
            }
            
            .price {
                font-size: 20px;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">P</div>
        
        <img src="${data.imageUrl}" alt="${data.title}" class="product-image" onerror="this.style.display='none'">
        
        <h1 class="title">${data.title}</h1>
        <p class="description">${data.description}</p>
        
        ${data.price != null && data.price! > 0 ? '<div class="price">R\$ ${data.price!.toStringAsFixed(2).replaceAll('.', ',')}</div>' : ''}
        
        <button class="download-btn pulse" onclick="downloadApp()">
            📱 Baixar Promobell Grátis
        </button>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">🔔</span>
                <span>Alertas de promoções em tempo real</span>
            </div>
            <div class="feature">
                <span class="feature-icon">💰</span>
                <span>Cupons de desconto exclusivos</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🎯</span>
                <span>Ofertas personalizadas para você</span>
            </div>
        </div>
        
        <p class="app-info">
            Baixe o app Promobell para ver esta oferta e muito mais!
        </p>
    </div>
    
    <script>
        function downloadApp() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            
            if (/android/i.test(userAgent)) {
                window.open('https://play.google.com/store/apps/details?id=com.promobell.app', '_blank');
            } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                window.open('https://apps.apple.com/br/app/promobell/id1234567890', '_blank');
            } else {
                // Desktop ou outros dispositivos - priorizar Android
                window.open('https://play.google.com/store/apps/details?id=com.promobell.app', '_blank');
            }
        }
        
        // Tentar abrir o app automaticamente após um delay
        setTimeout(() => {
            const deepLink = 'promobell://app${data.originalUrl}';
            const hiddenLink = document.createElement('a');
            hiddenLink.href = deepLink;
            hiddenLink.style.display = 'none';
            document.body.appendChild(hiddenLink);
            hiddenLink.click();
            document.body.removeChild(hiddenLink);
        }, 1500);
        
        // Detectar se o usuário voltou à página (app não abriu)
        let hidden, visibilityChange;
        if (typeof document.hidden !== "undefined") {
            hidden = "hidden";
            visibilityChange = "visibilitychange";
        } else if (typeof document.msHidden !== "undefined") {
            hidden = "msHidden";
            visibilityChange = "msvisibilitychange";
        } else if (typeof document.webkitHidden !== "undefined") {
            hidden = "webkitHidden";
            visibilityChange = "webkitvisibilitychange";
        }
        
        function handleVisibilityChange() {
            if (!document[hidden]) {
                // Usuário voltou à página, app provavelmente não abriu
                console.log('App detection: User returned to page');
            }
        }
        
        if (typeof document.addEventListener !== "undefined" && hidden !== undefined) {
            document.addEventListener(visibilityChange, handleVisibilityChange, false);
        }
    </script>
</body>
</html>
    ''';

    // Substituir o conteúdo da página
    web.document.documentElement?.innerHTML = redirectHtml.toJS;
  }

  void _redirectToPlayStore() {
    final userAgent = web.window.navigator.userAgent;
    
    if (userAgent.contains('Android')) {
      web.window.location.assign('https://play.google.com/store/apps/details?id=com.promobell.app');
    } else if (userAgent.contains('iPhone') || userAgent.contains('iPad')) {
      web.window.location.assign('https://apps.apple.com/br/app/promobell/id1234567890');
    } else {
      web.window.location.assign('https://play.google.com/store/apps/details?id=com.promobell.app');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Scaffold(
        backgroundColor: Color(0xFF667eea),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    'P',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF667eea),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 24),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 16),
              Text(
                'Carregando...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Esta parte normalmente não será exibida, pois o JavaScript
    // irá substituir o conteúdo da página
    return Scaffold(
      backgroundColor: Color(0xFF667eea),
      body: Center(
        child: Container(
          padding: EdgeInsets.all(32),
          margin: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Redirecionando...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 16),
              CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }
}