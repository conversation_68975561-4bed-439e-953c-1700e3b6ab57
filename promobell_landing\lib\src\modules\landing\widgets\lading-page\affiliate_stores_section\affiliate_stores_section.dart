import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../../../../../core/widgets/layout/scroll_reveal.dart';
import 'find_and_enjoy_section.dart';

class AffiliateStoresSection extends StatelessWidget {
  const AffiliateStoresSection({super.key});

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 900;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.7],
          colors: [ColorOutlet.surface, ColorOutlet.paper],
        ),
      ),
      padding: EdgeInsets.only(bottom: isDesktop ? 106 : 32, top: isDesktop ? 0 : 70),
      child:
          isDesktop
              ? ResponsivePaddingWrapper(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(child: FindAndEnjoySection()),
                    SizedBox(width: 16),
                    BrandShowcaseRow(),
                    SizedBox(width: 90),
                  ],
                ),
              )
              : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsivePaddingWrapper.mobile(
                    minPadding: 16,
                    child: BrandShowcaseRow(isMobile: true),
                  ),
                  SizedBox(height: 44.5),
                  ResponsivePaddingWrapper.mobile(child: FindAndEnjoySection(isMobile: true)),
                ],
              ),
    );
  }
}

class BrandShowcaseRow extends StatelessWidget {
  final bool isMobile;

  const BrandShowcaseRow({this.isMobile = false, super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final totalPadding = isMobile ? 32.0 : 96.0;
        final totalWidth = constraints.maxWidth - totalPadding;
        final unit = totalWidth / 2.0;

        final size = unit * 1.0;

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Column(
              spacing: 30,
              children: [
                ScrollReveal(
                  offset: Offset(0, 3),
                  child: Image.asset('assets/logos/meli.png', width: isMobile ? size : 220),
                ),
                ScrollReveal(
                  offset: Offset(0, 3),
                  child: Image.asset('assets/logos/amazon.png', width: isMobile ? size : 220),
                ),
              ],
            ),
            SizedBox(width: 30),
            Column(
              spacing: 30,
              children: [
                ScrollReveal(
                  offset: Offset(0, 2),
                  child: Image.asset('assets/logos/shopee.png', width: isMobile ? size : 220),
                ),
                ScrollReveal(
                  offset: Offset(0, 1),
                  child: Image.asset('assets/logos/magalu.png', width: isMobile ? size : 220),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
