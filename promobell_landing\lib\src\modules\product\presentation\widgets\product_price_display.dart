import 'package:flutter/material.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/text_pattern.dart';
import 'package:promobell_landing/src/core/utils/format_currency.dart';
import 'package:promobell_landing/src/modules/product/presentation/controllers/product_controller.dart';

class ProductPriceDisplay extends StatelessWidget {
  const ProductPriceDisplay({super.key, required this.controller});

  final ProductController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (controller.product!.precoAntigo > 0)
          TextPattern.customText(
            text: formatCurrency(controller.product!.precoAntigo),
            fontSize: 14,
            color: ColorOutlet.contentGhost,
            decoration: TextDecoration.lineThrough,
          ),
        if (controller.product!.precoAtual > 0)
          TextPattern.customText(
            text: formatCurrency(controller.product!.precoAtual),
            fontSize: 24,
            color: ColorOutlet.contentPrimary,
            fontWeightOption: FontWeightOption.bold,
            overflow: TextOverflow.ellipsis,
          ),
        if (controller.product!.precoAtual == 0)
          TextPattern.customText(
            text: 'Produto não disponível',
            fontSize: 18,
            color: ColorOutlet.contentPrimary,
            fontWeightOption: FontWeightOption.medium,
          ),
      ],
    );
  }
}
