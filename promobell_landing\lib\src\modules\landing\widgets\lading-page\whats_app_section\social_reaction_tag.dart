import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';

class SocialReactionTag extends StatelessWidget {
  const SocialReactionTag({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 152,
      height: 40,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: ColorOutlet.filled,
        border: Border.all(color: ColorOutlet.systemBorderDisabled, width: 1),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 8,
        children: [
          Image.asset('assets/icons/❤️.png', width: 24, height: 24),
          Image.asset('assets/icons/🔥.png', width: 24, height: 24),
          Image.asset('assets/icons/😍.png', width: 24, height: 24),
          Image.asset('assets/icons/👍.png', width: 24, height: 24),
        ],
      ),
    );
  }
}
