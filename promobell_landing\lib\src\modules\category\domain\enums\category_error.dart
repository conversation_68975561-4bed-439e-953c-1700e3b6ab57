enum CategoryError { idNotProvided, invalidId, categoryNotFound, errorLoading }

String translateCategoryError(CategoryError error) {
  switch (error) {
    case CategoryError.idNotProvided:
      return 'ID da categoria não fornecido';
    case CategoryError.invalidId:
      return 'ID da categoria inválido';
    case CategoryError.categoryNotFound:
      return 'Erro ao carregar a categoria: $error';
    case CategoryError.errorLoading:
      return 'Erro ao carregar a categoria: $error';
  }
}
