class Product {
  final int id;
  final String plataforma;
  final String urlAfiliado;
  final String urlImagem;
  final String titulo;
  final String categoria;
  final String subcategoria;
  final String descricao;
  final double precoAtual;
  final double precoAntigo;
  final bool ativo;
  final bool menorPreco;
  final bool invalidProduct;

  const Product({
    required this.id,
    required this.plataforma,
    required this.urlAfiliado,
    required this.urlImagem,
    required this.titulo,
    required this.categoria,
    required this.subcategoria,
    required this.descricao,
    required this.precoAtual,
    required this.precoAntigo,
    required this.ativo,
    required this.menorPreco,
    required this.invalidProduct,
  });

  bool get isValid => !invalidProduct;

  static const Product mock = Product(
    id: 1,
    plataforma: 'Mercado Livre',
    urlAfiliado: 'https://www.mercadolivre.com.br/social/akipromocoes?...',
    urlImagem: 'https://encrypted-tbn0.gstatic.com/images?...',
    titulo: 'Produto não encontrado!',
    categoria: 'Produto não encontrado!',
    subcategoria: 'Produto não encontrado!',
    descricao: 'Produto não encontrado!',
    precoAtual: 0.0,
    precoAntigo: 0.0,
    ativo: true,
    menorPreco: true,
    invalidProduct: false,
  );
}
