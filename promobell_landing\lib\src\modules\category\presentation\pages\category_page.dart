import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:meta_seo/meta_seo.dart';
import 'package:promobell_landing/src/modules/category/presentation/widgets/category_avatar.dart';
import 'package:promobell_landing/src/modules/category/presentation/widgets/category_profile_card.dart';
import 'package:promobell_landing/src/modules/landing/page/empty_state.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/categories_section/categories_section.dart';
import 'package:promobell_landing/src/modules/landing/widgets/onboarding_download_section.dart';

import '../../../../core/helpers/parse_helper.dart';
import '../../../../core/seo/seo.dart';
import '../../../../core/theme/color_outlet.dart';
import '../../../../core/widgets/shared/no_mouse_drag_scroll_behavior.dart';
import '../../../landing/widgets/lading-page/landing_header/landing_header.dart';
import '../controllers/category_controller.dart';

class CategoryPage extends StatefulWidget {
  const CategoryPage({super.key});

  @override
  State<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends State<CategoryPage> implements Seo {
  final controller = Modular.get<CategoryController>();

  @override
  void initState() {
    super.initState();
    final categoryId = parseIntFromQueryParam('id');
    controller.loadCategoryData(categoryId);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        if (controller.loading) {
          return const Scaffold(body: Center(child: CircularProgressIndicator()));
        }
        if (kIsWeb) loadSeo();

        final category = controller.category;
        final followers = controller.followers;
        final product = controller.products;

        if (category == null) {
          return EmptyState();
        }

        final categoryColor = getFilteredColor(category.cor);

        return SelectionArea(
          child: Scaffold(
            backgroundColor: ColorOutlet.surface,
            body: ScrollConfiguration(
              behavior: NoMouseDragScrollBehavior(),
              child: Stack(
                children: [
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(height: 96, color: Color(0xFFFAFAFC).withValues(alpha: 80)),
                  ),
                  CustomScrollView(
                    slivers: [
                      const LandingHeader(center: true),
                      SliverToBoxAdapter(
                        child: Column(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  stops: [0.0, 0.37, 0.37, 1.0],
                                  colors: [categoryColor, categoryColor, ColorOutlet.paper, ColorOutlet.paper],
                                ),
                              ),
                              child: FixedWidthFittedSection(
                                width: 390,
                                child: Stack(
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(height: 72),
                                        CategoryProfileCard(
                                          nome: category.nome,
                                          bio: category.bio,
                                          followers: followers,
                                          products: product,
                                        ),
                                      ],
                                    ),
                                    CategoryAvatar(categoryColor: categoryColor, fotoPequena: category.fotoPequena),
                                  ],
                                ),
                              ),
                            ),
                            OnboardingDownloadSection(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void loadSeo() {
    final category = controller.category;
    if (category == null) return;

    final nome = category.nome;
    final bio = category.bio;
    final foto = category.fotoPequena;

    final meta = MetaSEO();
    meta.ogTitle(ogTitle: 'Categoria $nome - Promobell');
    meta.description(description: bio);
    meta.ogDescription(ogDescription: bio);

    String imageUrl;
    if (foto.startsWith('http')) {
      imageUrl = foto;
    } else {
      final nomeUrl = nome.toLowerCase().replaceAll(' ', '');
      final nomePath = nome.toLowerCase().replaceAll(' ', '_');
      imageUrl = 'https://promobell.com.br/assets/categorias/$nomePath/category-image-$nomeUrl.png';
    }

    meta.ogImage(ogImage: imageUrl);
    meta.propertyContent(property: 'og:type', content: 'website');
    meta.propertyContent(property: 'og:url', content: 'https://promobell.com.br/category?id=${category.id}');
    meta.keywords(
      keywords: '''
    ofertas, cupons de desconto, frete grátis, alertas de preço, produtos em promoção, marketplaces, Amazon, Magalu, Shopee, Mercado Livre, app de economia, descontos online, notificações de promoções, rede social de ofertas, $nome, categorias personalizadas, buscar ofertas, economia inteligente, app de ofertas, alertas inteligentes, cupons exclusivos, o app alerta você economiza, promobell
  ''',
    );

    meta.propertyContent(property: 'og:site_name', content: 'Promobell - O app alerta, você economiza');
  }
}
