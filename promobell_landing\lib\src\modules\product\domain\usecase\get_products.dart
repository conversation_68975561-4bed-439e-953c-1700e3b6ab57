import 'package:dartz/dartz.dart';

import '../entities/product.dart';
import '../enums/product_error.dart';
import '../repositories/i_product_repository.dart';

class GetProduct {
  final IProductRepository repository;

  GetProduct(this.repository);

  Future<Either<ProductError, Product?>> call(int id) async {
    if (id <= 0) return Left(ProductError.invalidId);

    try {
      final product = await repository.getProduct(id);
      if (product != null && product.isValid) {
        return Right(product);
      } else {
        return Left(ProductError.invalidProduct);
      }
    } catch (e) {
      return Left(ProductError.serverFailure);
    }
  }
}
