import 'package:flutter/material.dart';

import '../../domain/entities/product.dart';
import '../../domain/repositories/i_product_repository.dart';
import '../../domain/repositories/i_product_datasource.dart';

class ProductRepositoryImpl implements IProductRepository {
  final IProductDatasource datasource;

  ProductRepositoryImpl(this.datasource);

  @override
  Future<Product?> getProduct(int id) async {
    try {
      final result = await datasource.getProductById(id);
      return result?.toEntity();
    } catch (e) {
      debugPrint('Erro ao buscar produto com id $id: $e');
      return null;
    }
  }
}
