import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';

class TwoPartText extends StatelessWidget {
  final String text;
  final String description;

  const TwoPartText({required this.text, required this.description, super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        TextPattern.customText(
          text: text,
          fontSize: 14,
          fontWeightOption: FontWeightOption.regular,
          fontFamilyOption: FontFamilyOption.roboto,
          color: ColorOutlet.contentSecondary,
        ),
        TextPattern.customText(
          text: description,
          fontSize: 14,
          fontWeightOption: FontWeightOption.semiBold,
          fontFamilyOption: FontFamilyOption.roboto,
          color: ColorOutlet.contentSecondary,
        ),
      ],
    );
  }
}
