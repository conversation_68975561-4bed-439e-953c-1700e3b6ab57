import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';
import '../../../../../core/widgets/layout/shadow_container.dart';
import 'two_part_text.dart';

class OfferDetailCard extends StatelessWidget {
  const OfferDetailCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.bottomCenter,
      children: [
        Positioned(left: -20, bottom: -10, child: <PERSON><PERSON>ontaine<PERSON>(height: 400, width: 360)),
        Column(
          children: [
            Container(
              width: 342,
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(color: ColorOutlet.contentTertiary, borderRadius: BorderRadius.circular(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: ColorOutlet.systemBorderDisabled, width: 0.66),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Image.asset('assets/images/phone.png', width: 318, height: 170),
                  ),
                  SizedBox(height: 10),
                  TextPattern.customText(
                    text: '🔥 Xiaomi Poco C65 Dual SIM 256 GB Azul 8 GB\nRAM',
                    fontSize: 14,
                    fontWeightOption: FontWeightOption.bold,
                    fontFamilyOption: FontFamilyOption.roboto,
                    color: ColorOutlet.contentSecondary,
                  ),
                  SizedBox(height: 16),
                  TwoPartText(text: 'De ', description: '1.249,00'),
                  TwoPartText(text: 'Por ', description: '1.025,10 na Amazon'),
                  TwoPartText(text: 'Frete: ', description: 'Grátis'),
                  TwoPartText(text: 'Use o cupom: ', description: 'BELL10'),
                  SizedBox(height: 16),
                  TextPattern.customText(
                    text: '🛒 Ver oferta:',
                    fontSize: 14,
                    fontWeightOption: FontWeightOption.semiBold,
                    fontFamilyOption: FontFamilyOption.roboto,
                    color: ColorOutlet.contentSecondary,
                  ),
                  TextPattern.customText(
                    text: 'https://promobell.com.br/oferta/9876',
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                    decorationColor: ColorOutlet.contentPrimary,
                    fontWeightOption: FontWeightOption.regular,
                    fontFamilyOption: FontFamilyOption.roboto,
                    color: ColorOutlet.contentPrimary,
                  ),
                  SizedBox(height: 28),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
