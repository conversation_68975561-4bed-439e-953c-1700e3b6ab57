import 'package:web/web.dart' as web;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:js_interop';

class DeferredLinksService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  /// Resolve um deferred link e retorna os dados originais
  Future<DeferredLinkData?> resolveDeferredLink(String linkId) async {
    try {
      final response = await _supabase
          .from('deferred_links')
          .select()
          .eq('link_id', linkId)
          .single();

      // Incrementar contador de cliques
      await _incrementClickCount(linkId);
      return DeferredLinkData.fromMap(response);
    } catch (e) {
      // Erro ao resolver deferred link: $e
    }
    return null;
  }

  /// Incrementa o contador de cliques do deferred link
  Future<void> _incrementClickCount(String linkId) async {
    try {
      await _supabase.rpc('increment_deferred_link_clicks', params: {
        'link_id_param': linkId,
      });
    } catch (e) {
      // Erro ao incrementar contador de cliques: $e
    }
  }

  /// Detecta se o app está instalado e redireciona adequadamente
  Future<void> handleDeferredLink(String linkId) async {
    final deferredData = await resolveDeferredLink(linkId);
    
    if (deferredData == null) {
      _redirectToPlayStore();
      return;
    }

    // Tentar abrir o app com o deep link
    final deepLinkUrl = 'promobell://app${deferredData.originalUrl}';
    
    // Criar um iframe invisível para tentar abrir o app
    final iframe = web.HTMLIFrameElement()
      ..src = deepLinkUrl
      ..style.display = 'none';
    
    web.document.body?.append(iframe);
    
    // Aguardar um tempo para ver se o app abre
    await Future.delayed(Duration(milliseconds: 2000));
    
    // Se chegou até aqui, provavelmente o app não está instalado
    // Mostrar página de redirecionamento
    _showRedirectPage(deferredData);
    
    // Remover o iframe
    iframe.remove();
  }

  /// Mostra a página de redirecionamento com informações do produto/categoria
  void _showRedirectPage(DeferredLinkData data) {
    final web.Element? body = web.document.body;
    if (body == null) return;

    // Limpar o conteúdo atual
     body.innerHTML = ''.toJS;

    // Criar o HTML da página de redirecionamento
    final redirectHtml = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.title}</title>
    <meta name="description" content="${data.description}">
    <meta property="og:title" content="${data.title}">
    <meta property="og:description" content="${data.description}">
    <meta property="og:image" content="${data.imageUrl}">
    <meta property="og:type" content="product">
    <meta property="og:url" content="${web.window.location.href}">
    <style>
        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 32px;
            max-width: 400px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        .product-image {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 12px;
            margin: 0 auto 20px;
            display: block;
        }
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .description {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        .price {
            font-size: 18px;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 24px;
        }
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .app-info {
            font-size: 12px;
            color: #999;
            margin-top: 16px;
        }
        @media (max-width: 480px) {
            .container {
                margin: 20px;
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <img src="${data.imageUrl}" alt="${data.title}" class="product-image" onerror="this.style.display='none'">
        <h1 class="title">${data.title}</h1>
        <p class="description">${data.description}</p>
        ${data.price != null && data.price! > 0 ? '<div class="price">R\$ ${data.price!.toStringAsFixed(2).replaceAll('.', ',')}</div>' : ''}
        <button class="download-btn" onclick="downloadApp()">
            📱 Baixar Promobell
        </button>
        <p class="app-info">
            Baixe o app Promobell para ver esta oferta e receber alertas de promoções!
        </p>
    </div>
    
    <script>
        function downloadApp() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            
            if (/android/i.test(userAgent)) {
                window.location.href = 'https://play.google.com/store/apps/details?id=com.promobell.app';
            } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                window.location.href = 'https://apps.apple.com/br/app/promobell/id1234567890';
            } else {
                // Desktop ou outros dispositivos
                window.location.href = 'https://play.google.com/store/apps/details?id=com.promobell.app';
            }
        }
        
        // Tentar abrir o app automaticamente após um delay
        setTimeout(() => {
            const deepLink = 'promobell://app${data.originalUrl}';
            window.location.href = deepLink;
        }, 1000);
    </script>
</body>
</html>
    ''';

    // Substituir o conteúdo da página
     web.document.documentElement?.innerHTML = redirectHtml.toJS;
  }

  /// Redireciona para a Play Store
   void _redirectToPlayStore() {
     web.window.location.assign('https://play.google.com/store/apps/details?id=com.promobell.app');
   }


}

/// Classe para representar os dados de um deferred link
class DeferredLinkData {
  final String linkId;
  final String originalUrl;
  final String title;
  final String description;
  final String imageUrl;
  final double? price;
  final DateTime createdAt;
  final DateTime expiresAt;
  final int clickCount;

  DeferredLinkData({
    required this.linkId,
    required this.originalUrl,
    required this.title,
    required this.description,
    required this.imageUrl,
    this.price,
    required this.createdAt,
    required this.expiresAt,
    required this.clickCount,
  });

  factory DeferredLinkData.fromMap(Map<String, dynamic> map) {
    return DeferredLinkData(
      linkId: map['link_id'] ?? '',
      originalUrl: map['original_url'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['image_url'] ?? '',
      price: map['price']?.toDouble(),
      createdAt: DateTime.parse(map['created_at']),
      expiresAt: DateTime.parse(map['expires_at']),
      clickCount: map['click_count'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'link_id': linkId,
      'original_url': originalUrl,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'price': price,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'click_count': clickCount,
    };
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}