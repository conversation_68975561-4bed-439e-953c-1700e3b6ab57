import 'package:flutter/material.dart';

import '../../../core/theme/color_outlet.dart';
import '../../../core/theme/svg_icons.dart';
import '../../../core/theme/text_pattern.dart';
import '../../../core/widgets/buttons/safe_browsing_widget.dart';
import '../../../core/widgets/buttons/store_badge_button.dart';
import '../page/empty_state.dart';

class OnboardingDownloadSection extends StatelessWidget {
  const OnboardingDownloadSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorOutlet.paper,
      child: Container(
        decoration: BoxDecoration(
          color: ColorOutlet.surface,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
        ),
        child: Column(
          children: [
            FixedWidthFittedSection(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 32),
                  TextPattern.customText(
                    text: 'Entre, siga e economize de\nverdade',
                    fontWeightOption: FontWeightOption.bold,
                    fontSize: 24,
                  ),
                  SizedBox(height: 8),
                  TextPattern.customText(
                    text:
                        'Encontre seu produto seguindo perfis que têm tudo a ver com você. O Promobell te alerta sempre que rolar uma oferta ou um cupom.',
                    color: ColorOutlet.contentGhost,
                    fontSize: 14,
                  ),
                  SizedBox(height: 24),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final maxWidth = constraints.maxWidth;
                      return Row(
                        children: [
                          Column(
                            children: [
                              SizedBox(
                                width: maxWidth,
                                child: StoreBadgeButton(
                                  isMobile: true,
                                  svgIcon: SvgIcons.playStore,
                                  nameStore: 'Google Play',
                                  onAppStorePressed: () {},
                                  width: 326,
                                ),
                              ),
                              SizedBox(height: 24),
                              SizedBox(
                                width: maxWidth,
                                child: StoreBadgeButton(
                                  isMobile: true,
                                  svgIcon: SvgIcons.apple,
                                  nameStore: 'Apple Store',
                                  onAppStorePressed: () {},
                                  width: 326,
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                  SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [SafeBrowsingWidget(colorText: ColorOutlet.contentSecondary)],
                  ),
                  SizedBox(height: 24),
                ],
              ),
            ),
            FixedWidthFittedSection(
              width: 390,
              child: Column(
                spacing: 8,
                children: [
                  TextPattern.customText(
                    text: '© Promobell. Todos os direitos reservados.',
                    color: ColorOutlet.contentGhost,
                    fontSize: 12,
                    textAlign: TextAlign.center,
                  ),
                  TextPattern.customText(
                    text:
                        'Todas as marcas mencionadas são propriedade de seus respectivos detentores: Amazon.com, Inc., Ebazar.com.br LTDA, Magazine Luiza S/A',

                    color: ColorOutlet.contentGhost.withAlpha(90),
                    fontSize: 12,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
