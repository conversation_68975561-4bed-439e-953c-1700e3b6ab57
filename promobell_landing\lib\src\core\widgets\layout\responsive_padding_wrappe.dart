import 'package:flutter/material.dart';

class ResponsivePaddingWrapper extends StatelessWidget {
  final Widget? child;
  final Widget? adaptiveChild;
  final Widget? stack;
  final double paddingPercent;
  final double maxWidth;
  final double minPadding;
  final double maxPadding;
  final bool rightPadding;
  final bool isMobile;

  const ResponsivePaddingWrapper({
    super.key,
    this.child,
    this.adaptiveChild,
    this.stack,
    this.paddingPercent = 0.15,
    this.maxWidth = 1040,
    this.minPadding = 16,
    this.maxPadding = 450,
    this.rightPadding = false,
    this.isMobile = false,
  });

  const ResponsivePaddingWrapper.mobile({
    super.key,
    required Widget child,
    this.stack,
    this.paddingPercent = 0.15,
    this.maxWidth = 422.0,
    this.minPadding = 48,
    this.maxPadding = 450,
    this.rightPadding = false,
  }) : adaptiveChild = child,
       child = null,
       isMobile = true;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final horizontalPadding = ((screenWidth - 1040).clamp(minPadding * 2, maxPadding * 2)) / 2;
        final availableWidth = screenWidth.clamp(0.0, maxWidth);

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: maxWidth),
              child: Stack(
                clipBehavior: Clip.none,
                alignment: rightPadding ? Alignment.bottomCenter : Alignment.center,
                children: [
                  if (isMobile) Center(child: SizedBox(width: availableWidth, child: adaptiveChild)),
                  child ?? const SizedBox.shrink(),
                  if (rightPadding) stack ?? const SizedBox(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
